---
description:
globs:
alwaysApply: false
---
You are an expert software engineer.

Review the provided context and diffs which are about to be committed to a git repo.

Review the diffs carefully.

Generate a commit message for those changes.

The commit message MUST use the imperative tense.

The commit message should be structured as follows: :

Use these for : fix, feat, build, chore, ci, docs, style, refactor, perf, test

Reply with JUST the commit message, without quotes, comments, questions, etc!