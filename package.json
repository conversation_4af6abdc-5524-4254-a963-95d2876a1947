{"name": "memoir-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "pm2:dev": "pm2 start ecosystem.config.js --env development", "pm2:prod": "pm2 start ecosystem.config.js --env production", "pm2:reload": "pm2 reload memoir-app", "pm2:stop": "pm2 stop memoir-app", "pm2:delete": "pm2 delete memoir-app", "pm2:logs": "pm2 logs memoir-app", "pm2:monit": "pm2 monit", "pm2:startup": "pm2 startup", "pm2:save": "pm2 save"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@uiw/react-amap": "^7.1.8", "@uiw/react-amap-api-loader": "^7.1.8", "@uiw/react-amap-map": "^7.1.8", "ali-oss": "^6.23.0", "axios": "^1.9.0", "framer-motion": "^12.12.1", "graceful-fs": "^4.2.11", "next": "15.3.2", "pm2": "^6.0.8", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/ali-oss": "^6.16.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.17", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.4.35", "postcss-import": "^16.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}