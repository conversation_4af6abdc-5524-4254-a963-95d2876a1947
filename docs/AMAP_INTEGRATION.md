# 高德地图API集成指南

本文档介绍如何在Memoir App中集成高德地图API来自动获取城市的经纬度坐标。

## 功能特性

- 🔍 **智能城市搜索**: 输入城市名称自动搜索匹配的城市
- 🌍 **多语言支持**: 支持中文和英文城市名称搜索
- 📍 **自动坐标填充**: 选择城市后自动填充经纬度坐标
- ⚡ **防抖搜索**: 避免频繁API调用，提升用户体验
- 🎯 **精确定位**: 基于高德地图的准确地理数据

## 配置步骤

### 1. 申请高德地图API Key

1. 访问 [高德开放平台](https://console.amap.com/)
2. 注册并登录账号
3. 创建应用，选择 "Web服务" 类型
4. 获取 API Key

### 2. 配置环境变量

1. 复制环境变量示例文件：
   ```bash
   cp .env.local.example .env.local
   ```

2. 在 `.env.local` 文件中配置你的API Key：
   ```env
   NEXT_PUBLIC_AMAP_API_KEY=your_actual_api_key_here
   ```

### 3. 重启开发服务器

```bash
npm run dev
```

## 使用方法

### 在LocationModal中使用

1. 点击"添加地点"按钮
2. 在"城市名称"输入框中输入城市名称
3. 系统会自动搜索匹配的城市
4. 从下拉列表中选择正确的城市
5. 经纬度会自动填充
6. 完成其他信息填写并保存

### 支持的搜索格式

- **中文城市名**: 北京、上海、广州
- **英文城市名**: Beijing, Shanghai, Guangzhou  
- **带"市"后缀**: 北京市、上海市
- **省份名称**: 广东省、江苏省

## API限制

### 免费版限制
- 个人开发者：每日30万次免费调用
- 企业开发者：根据套餐不同

### 调用频率
- 建议控制在每秒不超过200次
- 本应用已实现500ms防抖，有效控制调用频率

## 错误处理

应用会处理以下错误情况：

1. **API Key未配置**: 显示配置提示
2. **网络错误**: 显示网络连接错误
3. **API调用失败**: 显示具体错误信息
4. **搜索无结果**: 提示用户尝试其他关键词

## 技术实现

### 核心文件

- `src/lib/services/geocodingService.ts`: 地理编码服务
- `src/components/LocationModal.tsx`: 位置添加组件
- `src/lib/config/env.ts`: 环境变量配置

### 主要功能

1. **地理编码服务** (`geocodingService.ts`)
   - 封装高德地图API调用
   - 提供城市搜索和地理编码功能
   - 错误处理和结果格式化

2. **智能搜索** (`LocationModal.tsx`)
   - 防抖搜索避免频繁调用
   - 实时搜索结果展示
   - 点击外部关闭搜索结果

3. **用户体验优化**
   - 加载状态指示
   - 错误信息提示
   - 自动坐标填充
   - 搜索结果高亮

## 开发注意事项

1. **API Key安全**: 
   - 仅在客户端使用，注意API Key的域名限制
   - 生产环境建议配置域名白名单

2. **性能优化**:
   - 实现了搜索防抖，避免频繁调用
   - 缓存搜索结果（可选）

3. **错误处理**:
   - 优雅处理API调用失败
   - 提供用户友好的错误提示

## 故障排除

### 常见问题

1. **搜索无结果**
   - 检查API Key是否正确配置
   - 确认网络连接正常
   - 尝试不同的搜索关键词

2. **API调用失败**
   - 检查API Key是否有效
   - 确认是否超出调用限制
   - 查看浏览器控制台错误信息

3. **坐标不准确**
   - 高德地图数据可能存在偏差
   - 可以手动调整经纬度坐标

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 检查Network面板的API调用情况
4. 确认环境变量是否正确加载

## 更新日志

- **v1.0.0**: 初始版本，支持基本的城市搜索和坐标获取
- 支持中英文搜索
- 实现防抖搜索
- 添加错误处理和用户提示
