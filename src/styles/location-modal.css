/* 地点模态框样式 */
.location-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-modal, rgba(0, 0, 0, 0.5));
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  animation: fadeIn 0.3s ease;
}

.location-modal-container {
  background-color: var(--bg-card, white);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg, 0 25px 50px -12px rgba(0, 0, 0, 0.25));
  width: 100%;
  max-width: 28rem;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--border-primary, transparent);
}

/* 模态框头部 */
.location-modal-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-primary, #e5e7eb);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  color: var(--text-primary, #111827);
}

.location-modal-title i {
  margin-right: 0.5rem;
  color: var(--accent-primary, #3b82f6);
}

.location-modal-actions {
  display: flex;
  align-items: center;
}

.add-location-btn {
  margin-right: 0.75rem;
  width: 2rem;
  height: 2rem;
  background-color: var(--bg-tertiary, #dbeafe);
  color: var(--accent-primary, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.add-location-btn:hover {
  background-color: var(--bg-secondary, #bfdbfe);
}

.close-modal-btn {
  color: var(--text-secondary, #6b7280);
  transition: color 0.2s ease;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
}

.close-modal-btn:hover {
  color: var(--text-primary, #374151);
}

/* 错误和成功消息 */
.error-message {
  color: var(--accent-danger, #ef4444);
  padding: 1rem;
  border-bottom: 1px solid var(--border-primary, #e5e7eb);
  background-color: var(--bg-error, rgba(239, 68, 68, 0.05));
}

.success-message {
  color: var(--accent-success, #10b981);
  padding: 1rem;
  border-bottom: 1px solid var(--border-primary, #e5e7eb);
  background-color: rgba(16, 185, 129, 0.05);
  animation: fadeIn 0.3s ease;
}

.success-message i {
  margin-right: 0.5rem;
}

/* 添加地点表单 */
.add-location-form {
  padding: 1rem;
  border-bottom: 1px solid var(--border-primary, #e5e7eb);
  animation: slideDown 0.3s ease;
}

.form-tip {
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 0.375rem;
}

.form-tip-content {
  display: flex;
  align-items: flex-start;
}

.form-tip i {
  color: var(--accent-primary, #3b82f6);
  margin-top: 0.125rem;
  margin-right: 0.5rem;
}

.form-tip-text {
  font-size: 0.875rem;
  color: var(--accent-primary, #1d4ed8);
}

.form-space-y-4 > * + * {
  margin-top: 1rem;
}

.form-group {
  position: relative;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary, #374151);
  margin-bottom: 0.25rem;
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-primary, #d1d5db);
  border-radius: 0.375rem;
  background-color: var(--bg-card, white);
  color: var(--text-primary, #111827);
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-primary, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input-with-icon {
  padding-right: 2.5rem;
}

.input-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted, #9ca3af);
}

/* 搜索结果下拉 */
.search-results {
  position: absolute;
  z-index: 10;
  width: 100%;
  margin-top: 0.25rem;
  background-color: var(--bg-card, white);
  border: 1px solid var(--border-primary, #d1d5db);
  border-radius: 0.375rem;
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  max-height: 15rem;
  overflow-y: auto;
}

.search-result-item {
  padding: 0.75rem;
  cursor: pointer;
  border-bottom: 1px solid var(--border-primary, #f3f4f6);
  transition: background-color 0.2s ease;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

.search-result-name {
  font-weight: 500;
  color: var(--text-primary, #111827);
}

.search-result-address {
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
}

/* 搜索错误 */
.search-error {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--accent-danger, #dc2626);
}

.search-error i {
  margin-right: 0.25rem;
}

/* 网格布局 */
.grid-cols-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

/* 表单按钮 */
.form-submit-container {
  display: flex;
  justify-content: flex-end;
}

.form-submit-btn {
  background-color: var(--accent-primary, #2563eb);
  color: var(--text-inverse, white);
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
  border: none;
  cursor: pointer;
  font-weight: 500;
}

.form-submit-btn:hover:not(:disabled) {
  background-color: var(--accent-hover, #1d4ed8);
}

.form-submit-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 删除确认对话框 */
.delete-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-modal, rgba(0, 0, 0, 0.5));
  z-index: 60;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.delete-confirm-container {
  background-color: var(--bg-card, white);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg, 0 25px 50px -12px rgba(0, 0, 0, 0.25));
  width: 100%;
  max-width: 24rem;
  padding: 1.25rem;
  animation: fadeIn 0.3s ease;
  border: 1px solid var(--border-primary, transparent);
}

.delete-confirm-title {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  color: var(--text-primary, #111827);
}

.delete-confirm-text {
  margin-bottom: 1rem;
  color: var(--text-secondary, #6b7280);
}

.delete-confirm-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.delete-cancel-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-primary, #d1d5db);
  border-radius: 0.375rem;
  color: var(--text-primary, #374151);
  background-color: var(--bg-card, white);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.delete-cancel-btn:hover {
  background-color: var(--bg-secondary, #f9fafb);
}

.delete-confirm-btn {
  padding: 0.5rem 1rem;
  background-color: var(--accent-danger, #dc2626);
  color: var(--text-inverse, white);
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.delete-confirm-btn:hover:not(:disabled) {
  background-color: #b91c1c;
}

.delete-confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 城市列表 */
.city-list-container {
  flex: 1;
  overflow: auto;
  padding: 1rem;
}

.loading-container {
  text-align: center;
  padding: 2rem 0;
  color: var(--text-secondary, #6b7280);
}

.loading-container i {
  margin-right: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 2rem 0;
  color: var(--text-secondary, #6b7280);
}

.empty-state i {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
  color: var(--text-muted, #d1d5db);
}

.city-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.city-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background-color: var(--bg-card, white);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
  transition: box-shadow 0.2s ease;
  border: 1px solid var(--border-primary, transparent);
}

.city-item:hover {
  box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
}

.city-color-indicator {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse, white);
  margin-right: 0.75rem;
  font-weight: 600;
}

.city-info {
  flex-grow: 1;
}

.city-name {
  font-weight: 500;
  color: var(--text-primary, #111827);
}

.city-date {
  font-size: 0.75rem;
  color: var(--text-secondary, #6b7280);
}

.city-delete-btn {
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  transform: scale(1);
  position: relative;
  border: none;
  background: none;
  cursor: pointer;
}

.city-delete-btn:not(:disabled) {
  color: var(--text-muted, #9ca3af);
}

.city-delete-btn:not(:disabled):hover {
  color: var(--accent-danger, #ef4444);
  background-color: rgba(239, 68, 68, 0.05);
  transform: scale(1.1);
}

.city-delete-btn:disabled {
  background-color: var(--bg-secondary, #f3f4f6);
  cursor: not-allowed;
  color: var(--text-muted, #6b7280);
}

.delete-tooltip {
  position: absolute;
  right: 100%;
  margin-right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--bg-tooltip, rgba(17, 24, 39, 0.8));
  color: var(--text-inverse, white);
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  white-space: nowrap;
  pointer-events: none;
}

.city-delete-btn:hover .delete-tooltip {
  opacity: 1;
}

/* 模态框底部 */
.location-modal-footer {
  padding: 0.75rem;
  border-top: 1px solid var(--border-primary, #e5e7eb);
  background-color: var(--bg-secondary, #f9fafb);
  display: flex;
  justify-content: center;
}

.location-count {
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
}

/* 城市颜色类 */
.bg-blue-500 {
  background-color: #3b82f6;
}

.bg-green-500 {
  background-color: #10b981;
}

.bg-purple-500 {
  background-color: #8b5cf6;
}

.bg-yellow-500 {
  background-color: #f59e0b;
}

.bg-pink-500 {
  background-color: #ec4899;
}

.bg-indigo-500 {
  background-color: #6366f1;
}

.bg-red-500 {
  background-color: #ef4444;
}

.bg-teal-500 {
  background-color: #14b8a6;
}

/* 深色主题特殊适配 */
[data-theme="dark"] .form-tip {
  background-color: rgba(88, 166, 255, 0.1);
  border-color: rgba(88, 166, 255, 0.3);
}

[data-theme="dark"] .search-result-item:hover {
  background-color: rgba(88, 166, 255, 0.1);
}

[data-theme="dark"] .city-delete-btn:not(:disabled):hover {
  background-color: rgba(248, 81, 73, 0.1);
}
