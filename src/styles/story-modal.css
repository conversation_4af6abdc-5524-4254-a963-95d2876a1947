/* 故事详情模态框样式 */
.story-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-modal, rgba(0, 0, 0, 0.85));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  padding: 20px;
  opacity: 1;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.story-modal-overlay.closing {
  opacity: 0;
}

.story-modal-container {
  display: flex;
  width: 95%;
  max-width: 1300px;
  height: 85vh;
  background-color: var(--bg-card, #fff);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-lg, 0 15px 40px rgba(0, 0, 0, 0.25));
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
  animation: modalAppear 0.5s forwards;
}

@keyframes modalAppear {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.story-modal-container.closing { 
  opacity: 0;
  transform: scale(0.95);
}

.story-modal-media {
  flex: 5;
  background-color: #000;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.story-modal-content {
  flex: 4;
  min-width: 420px;
  max-width: 550px;
  padding: 40px;
  position: relative;
  overflow-y: auto;
  background-color: var(--bg-secondary, #f8f6f2);
  background-image:
    linear-gradient(135deg, var(--bg-overlay, rgba(255,255,255,0.8)) 0%, var(--bg-tertiary, rgba(245,242,238,0.8)) 100%),
    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23d1c7b7' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  background-position: center;
  background-size: cover;
  border-left: 1px solid var(--border-primary, rgba(0,0,0,0.05));
  box-shadow: inset 5px 0 15px var(--shadow-sm, rgba(0,0,0,0.03));
  display: flex;
  flex-direction: column;
  /* 完全隐藏滚动条，同时保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  /* 添加装饰元素 */
  position: relative;
}

/* 添加装饰角落 */
.story-modal-content::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 15px;
  width: 40px;
  height: 40px;
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0 L40 0 L40 5 L5 5 L5 40 L0 40 Z' fill='rgba(106, 123, 217, 0.15)'/%3E%3C/svg%3E");
  opacity: 0.8;
  pointer-events: none;
}

.story-modal-content::after {
  content: '';
  position: absolute;
  bottom: 15px;
  right: 15px;
  width: 40px;
  height: 40px;
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M40 40 L0 40 L0 35 L35 35 L35 0 L40 0 Z' fill='rgba(106, 123, 217, 0.15)'/%3E%3C/svg%3E");
  opacity: 0.8;
  pointer-events: none;
}

/* 隐藏WebKit浏览器的滚动条 */
.story-modal-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
  width: 0;
  height: 0;
}

.story-close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background: var(--bg-overlay, rgba(255, 255, 255, 0.3));
  border: none;
  font-size: 1.2rem;
  color: var(--text-secondary, #666);
  cursor: pointer;
  z-index: 10;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  box-shadow: var(--shadow-sm, 0 2px 8px rgba(0,0,0,0.1));
}

.story-close-button:hover {
  color: var(--text-primary, #333);
  background: var(--bg-overlay, rgba(255, 255, 255, 0.9));
  transform: scale(1.05);
}

.story-header { 
  margin-bottom: 30px;
  position: relative;
  padding-bottom: 15px;
}

.story-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, var(--primary, #6a7bd9), rgba(106, 123, 217, 0.2));
  border-radius: 3px;
}

.story-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 15px;
  color: var(--text-primary, #2a2a2a);
  line-height: 1.2;
  letter-spacing: -0.5px;
  text-shadow: 1px 1px 0 var(--bg-overlay, rgba(255,255,255,0.8));
}

.story-date {
  font-size: 1rem;
  color: var(--text-secondary, #666);
  display: flex;
  align-items: center;
  gap: 8px;
}

.story-date::before {
  content: '\f073';
  font-family: 'Font Awesome 5 Free';
  font-weight: 400;
  font-size: 0.9rem;
  color: var(--accent-primary, #6a7bd9);
}

.story-body {
  margin-bottom: 30px;
  flex: 1;
  position: relative;
  padding: 20px;
  background: var(--bg-overlay, rgba(255,255,255,0.6));
  border-radius: 12px;
  box-shadow: var(--shadow-sm, 0 2px 10px rgba(0,0,0,0.03));
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.story-content {
  font-size: 1.05rem;
  line-height: 1.7;
  color: var(--text-primary, #333);
  white-space: pre-wrap;
  margin: 0;
  text-shadow: 0 1px 0 var(--bg-overlay, rgba(255,255,255,0.5));
}

.story-locations {
  margin-top: 30px;
  padding-top: 25px;
  border-top: 1px solid var(--border-primary, rgba(0, 0, 0, 0.08));
  position: relative;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary, #2a2a2a);
  margin: 0 0 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  text-shadow: 1px 1px 0 var(--bg-overlay, rgba(255,255,255,0.8));
}

.section-title i {
  color: var(--accent-primary, #6a7bd9);
  font-size: 1rem;
  filter: drop-shadow(1px 1px 0 var(--bg-overlay, rgba(255,255,255,0.8)));
}

.locations-list { 
  display: flex; 
  flex-direction: column; 
  gap: 12px; 
}

.location-item {
  display: flex;
  flex-direction: column;
  padding: 14px 18px;
  background-color: var(--bg-overlay, rgba(255, 255, 255, 0.7));
  border-radius: 10px;
  border-left: 3px solid var(--accent-primary, #6a7bd9);
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm, 0 2px 8px rgba(0, 0, 0, 0.05));
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
}

.location-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md, 0 5px 15px rgba(0, 0, 0, 0.08));
  background-color: var(--bg-overlay, rgba(255, 255, 255, 0.85));
}

.location-name {
  font-weight: 600;
  color: var(--text-primary, #2a2a2a);
  font-size: 1.05rem;
}

.location-description {
  font-size: 0.9rem;
  color: var(--text-secondary, #555);
  margin-top: 6px;
  line-height: 1.5;
}

.media-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.media-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  opacity: 0;
  transition: opacity 0.5s ease;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
}

.media-image.loaded {
  opacity: 1;
}

.media-video {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  opacity: 0;
  transition: opacity 0.5s ease;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.media-video.loaded {
  opacity: 1;
}

.media-loading-overlay,
.media-error-overlay {
  position: absolute;
  top: 0; 
  left: 0; 
  right: 0; 
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  z-index: 4;
}

.image-error-overlay { 
  flex-direction: column; 
  color: #fff; 
}

.image-error-overlay i { 
  font-size: 3rem; 
  color: #ff5252; 
  margin-bottom: 16px; 
}

.keyboard-shortcuts {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid var(--border-primary, rgba(0, 0, 0, 0.08));
  font-size: 0.9rem;
  color: var(--text-tertiary, #888);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: var(--bg-overlay, rgba(255, 255, 255, 0.4));
  padding: 12px 15px;
  border-radius: 8px;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  box-shadow: var(--shadow-sm, 0 2px 6px rgba(0, 0, 0, 0.02));
}

.keyboard-shortcuts i {
  color: var(--text-muted, #999);
  font-size: 0.85rem;
}

.media-placeholder, 
.media-loading, 
.media-error {
  display: flex; 
  flex-direction: column; 
  align-items: center; 
  justify-content: center;
  height: 100%; 
  width: 100%; 
  color: #8e8e8e; 
  background-color: #111;
}

.media-placeholder i, 
.media-error i { 
  font-size: 3rem; 
  margin-bottom: 16px; 
  color: #555; 
}

.media-error i { 
  color: #ff5252; 
}

.media-error p { 
  color: #fff; 
  text-align: center; 
  padding: 0 20px;
}

.loading-spinner {
  width: 40px; 
  height: 40px; 
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-left-color: #fff; 
  border-radius: 50%; 
  animation: spin 1s linear infinite;
}

.loading-spinner-container p { 
  color: #aaa; 
  margin-top: 16px; 
  font-size: 0.9rem; 
}

.retry-button {
  background-color: rgba(255, 255, 255, 0.15); 
  color: white; 
  border: none; 
  border-radius: 6px;
  padding: 10px 20px; 
  font-size: 0.9rem; 
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover { 
  background-color: rgba(255, 255, 255, 0.25); 
}

@keyframes spin { 
  to { 
    transform: rotate(360deg); 
  } 
}

.media-nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 5;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.media-nav-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
  opacity: 1;
}

.media-nav-button i {
  font-size: 1rem;
}

.media-nav-prev {
  left: 20px;
}

.media-nav-next {
  right: 20px;
}

.media-dots-nav {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 10px;
  z-index: 5;
}

.media-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.media-dot:hover {
  background-color: rgba(255, 255, 255, 0.6);
}

.media-dot.active {
  background-color: white;
  transform: scale(1.2);
}

@media (max-width: 992px) {
  .story-modal-container {
    flex-direction: column;
    height: 90vh;
    width: 95%;
  }
  
  .story-modal-media {
    flex: none;
    height: 40%;
  }
  
  .story-modal-content {
    flex: none;
    height: 60%;
    max-width: none;
    min-width: 0;
    width: 100%;
    padding: 25px;
  }
  
  .story-title {
    font-size: 1.6rem;
  }
  
  .media-nav-button {
    width: 36px;
    height: 36px;
  }
  
  .media-nav-prev {
    left: 10px;
  }
  
  .media-nav-next {
    right: 10px;
  }
}

@media (max-width: 576px) {
  .story-modal-container {
    height: 95vh;
    width: 100%;
    border-radius: 0;
  }
  
  .story-modal-overlay {
    padding: 0;
  }
  
  .story-modal-media {
    height: 35%;
  }
  
  .story-modal-content {
    height: 65%;
    padding: 20px;
  }
  
  .story-title {
    font-size: 1.4rem;
  }
  
  .story-content {
    font-size: 1rem;
  }
  
  .story-header::after {
    width: 50px;
  }
}

/* 深色主题特殊适配 */
[data-theme="dark"] .story-modal-content {
  background-image:
    linear-gradient(135deg, var(--bg-overlay, rgba(22, 27, 34, 0.8)) 0%, var(--bg-tertiary, rgba(33, 38, 45, 0.8)) 100%),
    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%2330363d' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
}

[data-theme="dark"] .story-title {
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .story-content {
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .section-title {
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .section-title i {
  filter: drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.5));
}

[data-theme="dark"] .story-close-button {
  background: rgba(22, 27, 34, 0.8);
  color: var(--text-secondary);
}

[data-theme="dark"] .story-close-button:hover {
  background: rgba(33, 38, 45, 0.9);
  color: var(--text-primary);
}