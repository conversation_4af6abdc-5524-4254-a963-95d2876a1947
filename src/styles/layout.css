/* 头部操作按钮样式 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-logout-btn {
  background-color: transparent;
  border: 1px solid var(--border-primary);
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease-in-out;
  color: var(--text-primary);
}

.header-logout-btn:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-secondary);
}

/* 通知铃铛样式 */
.notification-bell {
  position: relative;
  cursor: pointer;
  padding: 8px;
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

.notification-bell:hover {
  color: var(--text-primary);
}

.notification-bell .badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: var(--accent-danger);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}