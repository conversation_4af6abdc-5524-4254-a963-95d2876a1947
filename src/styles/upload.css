/* 上传照片模态框样式 */

.upload-modal {
  max-width: 700px;
}

.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px;
  border: 2px dashed var(--border-primary, #ddd);
  border-radius: 8px;
  margin-bottom: 20px;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: var(--accent-primary, #4a90e2);
}

.upload-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 15px;
  transition: transform 0.2s;
}

.upload-button:hover {
  transform: scale(1.05);
}

.upload-button i {
  font-size: 3rem;
  color: var(--accent-primary, #4a90e2);
  margin-bottom: 10px;
}

.upload-button span {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-primary, #333);
}

.upload-info {
  margin-top: 15px;
  font-size: 0.85rem;
  color: var(--text-secondary, #777);
  text-align: center;
}

.preview-container {
  margin-top: 20px;
}

.preview-container h3 {
  font-size: 1rem;
  margin-bottom: 10px;
  color: #333;
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
  padding: 5px;
}

.preview-item {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-item img {
  width: 100%;
  height: 100px;
  object-fit: cover;
  display: block;
}

.remove-button {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.remove-button:hover {
  background-color: rgba(220, 53, 69, 0.8);
}

/* 深色主题适配 */
[data-theme="dark"] .upload-area {
  border-color: var(--border-primary, #444);
}

[data-theme="dark"] .upload-area:hover {
  border-color: var(--accent-primary, #5a9ae2);
}

[data-theme="dark"] .upload-button span {
  color: var(--text-primary, #eee);
}

[data-theme="dark"] .upload-info {
  color: var(--text-secondary, #aaa);
}

[data-theme="dark"] .preview-container h3 {
  color: var(--text-primary, #eee);
}

/* 添加或更新以下样式 */

.upload-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(59, 130, 246, 0.7);
  transition: width 0.3s ease;
}

.progress-text {
  position: relative;
  color: white;
  font-size: 12px;
  z-index: 1;
}

.preview-item {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-button {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.remove-button:hover {
  background-color: rgba(220, 38, 38, 0.8);
}

.remove-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.upload-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
  background-color: #f9fafb;
}

.upload-button:hover {
  border-color: #3b82f6;
  background-color: #f0f9ff;
}

.upload-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.upload-button i {
  font-size: 24px;
  margin-bottom: 8px;
  color: #6b7280;
}

.upload-info {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}

.upload-note {
  margin-top: 6px;
  font-style: italic;
  color: #4a90e2;
  font-size: 11px;
}

.upload-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

/* 视频预览相关样式 */
.preview-item video {
  width: 100%;
  height: 100px;
  object-fit: cover;
  display: block;
  background-color: #000;
}

.video-indicator {
  position: absolute;
  top: 5px;
  left: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* 上传成功消息样式 */
.upload-success-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #4caf50;
  color: white;
  padding: 12px 24px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translate(-50%, -20px); }
  10% { opacity: 1; transform: translate(-50%, 0); }
  90% { opacity: 1; transform: translate(-50%, 0); }
  100% { opacity: 0; transform: translate(-50%, -20px); }
} 