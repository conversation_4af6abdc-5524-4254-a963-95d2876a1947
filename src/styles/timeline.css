/* Timeline组件样式 */

/* 主题适配的浪漫背景 */
.romantic-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  pointer-events: none;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  opacity: 1;
  animation: gradientShift 15s ease infinite;
}

/* 浅色主题的背景渐变 */
:root[data-theme="light"] .bg-gradient {
  background: linear-gradient(135deg, rgba(255, 223, 237, 0.8) 0%, rgba(236, 213, 255, 0.8) 50%, rgba(208, 242, 255, 0.8) 100%);
  opacity: 0.7;
}

/* 深色主题的背景渐变 */
:root[data-theme="dark"] .bg-gradient {
  background: linear-gradient(135deg, rgba(13, 17, 23, 0.95) 0%, rgba(22, 27, 34, 0.95) 50%, rgba(33, 38, 45, 0.95) 100%);
  opacity: 1;
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 浅色主题的装饰背景 */
:root[data-theme="light"] .bg-decoration {
  background-image:
    radial-gradient(circle at 20% 30%, rgba(255, 182, 193, 0.3) 0%, transparent 8%),
    radial-gradient(circle at 75% 15%, rgba(186, 85, 211, 0.2) 0%, transparent 10%),
    radial-gradient(circle at 85% 50%, rgba(135, 206, 250, 0.2) 0%, transparent 12%),
    radial-gradient(circle at 30% 70%, rgba(255, 192, 203, 0.3) 0%, transparent 10%),
    radial-gradient(circle at 65% 85%, rgba(147, 112, 219, 0.2) 0%, transparent 8%);
}

/* 深色主题的装饰背景 */
:root[data-theme="dark"] .bg-decoration {
  background-image:
    radial-gradient(circle at 20% 30%, rgba(88, 166, 255, 0.1) 0%, transparent 8%),
    radial-gradient(circle at 75% 15%, rgba(139, 148, 158, 0.1) 0%, transparent 10%),
    radial-gradient(circle at 85% 50%, rgba(48, 54, 61, 0.2) 0%, transparent 12%),
    radial-gradient(circle at 30% 70%, rgba(33, 38, 45, 0.3) 0%, transparent 10%),
    radial-gradient(circle at 65% 85%, rgba(22, 27, 34, 0.2) 0%, transparent 8%);
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.timeline-container {
  margin-top: 2rem;
  position: relative;
}

.timeline-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  padding: 0.5rem;
}

.timeline-card {
  background: var(--bg-card);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  animation: fadeIn 0.5s ease forwards;
  opacity: 0;
  cursor: pointer;
  position: relative;
  outline: none;
  border: 1px solid var(--border-primary);
}

.timeline-card:focus {
  box-shadow: var(--shadow-lg), 0 0 0 2px var(--accent-primary);
}

.timeline-card:active {
  transform: scale(0.98);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.timeline-card:nth-child(1) { animation-delay: 0.1s; }
.timeline-card:nth-child(2) { animation-delay: 0.15s; }
.timeline-card:nth-child(3) { animation-delay: 0.2s; }
.timeline-card:nth-child(4) { animation-delay: 0.25s; }
.timeline-card:nth-child(5) { animation-delay: 0.3s; }
.timeline-card:nth-child(6) { animation-delay: 0.35s; }
.timeline-card:nth-child(7) { animation-delay: 0.4s; }
.timeline-card:nth-child(8) { animation-delay: 0.45s; }

.timeline-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-lg);
  z-index: 1;
}

.timeline-card-media {
  position: relative;
  height: 240px;
  overflow: hidden;
}

.timeline-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.8s ease;
}

.timeline-card:hover .timeline-card-image {
  transform: scale(1.08);
}

.timeline-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.2), transparent);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.timeline-card:hover .timeline-card-overlay {
  opacity: 1;
}

.view-story-btn {
  background: var(--bg-overlay);
  color: var(--accent-primary);
  padding: 12px 24px;
  border-radius: 30px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transform: translateY(15px);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
}

.timeline-card:hover .view-story-btn {
  transform: translateY(0);
}

.timeline-card:active .view-story-btn {
  background: var(--bg-card);
  transform: scale(0.95);
}

.timeline-card-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  color: var(--text-muted);
  font-size: 3rem;
}

.timeline-card-date {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8), rgba(0,0,0,0.3), transparent);
  color: white;
  padding: 1.2rem 1rem 0.8rem;
  font-size: 0.9rem;
  font-weight: 500;
  z-index: 2;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.timeline-card-content {
  padding: 1.8rem;
}

.timeline-card-title {
  margin: 0 0 0.75rem;
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
  display: inline-block;
}

.timeline-card-title:after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, var(--accent-primary), transparent);
}

.timeline-card-description {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.timeline-card-footer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-top: 0.8rem;
  border-top: 1px solid var(--border-primary);
}

.timeline-card-location {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-tertiary);
  font-size: 0.9rem;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
}

.load-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: var(--bg-overlay);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--accent-primary);
  border: 2px solid var(--accent-primary);
  border-radius: 30px;
  padding: 0.75rem 2.5rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.load-more-btn:hover {
  background-color: var(--accent-primary);
  color: var(--text-inverse);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.load-more-btn.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-primary);
  border-left-color: var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.no-more-events {
  color: var(--text-tertiary);
  font-size: 0.95rem;
  text-align: center;
  padding: 1rem;
  background: var(--bg-overlay);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 30px;
  box-shadow: var(--shadow-sm);
  display: inline-block;
  padding: 0.75rem 2rem;
  border: 1px solid var(--border-primary);
}

.events-count {
  text-align: center;
  color: var(--text-tertiary);
  font-size: 0.95rem;
  margin-top: 2rem;
  background: var(--bg-overlay);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-radius: 20px;
  display: inline-block;
  padding: 0.5rem 1.5rem;
  margin-left: auto;
  margin-right: auto;
  display: table;
  border: 1px solid var(--border-primary);
}

.scroll-to-top-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--accent-primary);
  color: var(--text-inverse);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 100;
}

.scroll-to-top-btn:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  flex-wrap: wrap;
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.search-filter-group {
  flex: 1;
  min-width: 300px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 1.2rem;
  color: var(--text-tertiary);
}

.search-box input {
  width: 100%;
  padding: 0.85rem 1rem 0.85rem 3rem;
  border: 1px solid var(--border-primary);
  background: var(--bg-overlay);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 30px;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
  color: var(--text-primary);
}

.search-box input:focus {
  border-color: var(--border-focus);
  background: var(--bg-card);
  box-shadow: var(--shadow-md);
}

.search-btn {
  position: absolute;
  right: 5px;
  background-color: var(--accent-primary);
  color: var(--text-inverse);
  border: none;
  border-radius: 30px;
  padding: 0.6rem 1.2rem;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.search-btn:hover {
  background-color: var(--accent-primary);
  filter: brightness(1.1);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.create-story-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
  border-radius: 30px;
  padding: 0.85rem 1.5rem;
  font-weight: 600;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.create-story-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background: var(--bg-overlay);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: var(--shadow-md);
  margin: 2rem 0;
  border: 1px solid var(--border-primary);
  color: var(--text-secondary);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--border-primary);
  border-left-color: var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  background-color: var(--bg-overlay);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--accent-danger);
  padding: 1.2rem;
  border-radius: 12px;
  margin-bottom: 2.5rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--accent-danger);
}

.empty-state {
  text-align: center;
  padding: 5rem 2rem;
  background: var(--bg-overlay);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);
}

.empty-state i {
  font-size: 3.5rem;
  color: var(--text-muted);
  margin-bottom: 1.5rem;
}

.empty-state h3 {
  font-size: 1.8rem;
  margin-bottom: 1.2rem;
  color: var(--text-primary);
}

.empty-state p {
  color: var(--text-secondary);
  margin-bottom: 2.5rem;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .timeline-grid {
    grid-template-columns: 1fr;
  }
  
  .timeline-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-filter-group {
    width: 100%;
  }
  
  .create-story-btn {
    width: 100%;
  }
  
  .bg-decoration {
    background-size: 200% 200%;
  }
} 