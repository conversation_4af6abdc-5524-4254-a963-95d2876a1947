/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-modal, rgba(0, 0, 0, 0.5));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: var(--bg-card, #fff);
  border-radius: 8px;
  box-shadow: var(--shadow-lg, 0 4px 12px rgba(0, 0, 0, 0.15));
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modal-appear 0.3s ease;
  border: 1px solid var(--border-primary, transparent);
}

/* 宽模态框样式 */
.wide-modal {
  max-width: 800px;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-primary, #eee);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: var(--text-primary, #333);
}

.close-button {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: var(--text-secondary, #999);
  transition: color 0.2s;
}

.close-button:hover {
  color: var(--text-primary, #333);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid var(--border-primary, #eee);
}

/* 步骤指示器样式 */
.step-indicator {
  display: flex;
  justify-content: space-between;
  margin: 0 20px;
  padding: 20px 0;
  position: relative;
}

.step-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #eee;
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: 2px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #666;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.step.active .step-number {
  background-color: #4a90e2;
  border-color: #4a90e2;
  color: white;
}

.step.completed .step-number {
  background-color: #4caf50;
  border-color: #4caf50;
  color: white;
}

.step-label {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.step.active .step-label {
  color: #4a90e2;
  font-weight: 500;
}

.step.completed .step-label {
  color: #4caf50;
}

/* 步骤内容样式 */
.step-content {
  min-height: 300px;
}

.step-subtitle {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 16px;
  color: #555;
}

/* 表单行样式 */
.form-row {
  display: flex;
  gap: 16px;
}

.form-row .form-group {
  flex: 1;
}

/* 地点卡片网格样式 */
.locations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.location-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.location-card:hover {
  border-color: #4a90e2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.location-card.selected {
  border-color: #4a90e2;
  background-color: rgba(74, 144, 226, 0.05);
}

.location-icon {
  font-size: 24px;
  color: #4a90e2;
  margin-bottom: 12px;
}

.location-details h4 {
  margin: 0 0 8px;
  font-size: 16px;
}

.location-details p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.location-select-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  color: #4a90e2;
  font-size: 16px;
}

/* 照片卡片网格样式 */
.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 20px;
}

.photo-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  aspect-ratio: 1;
}

.photo-card:hover {
  border-color: #4a90e2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.photo-card.selected {
  border-color: #4a90e2;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.photo-card.is-cover {
  border-color: #f5b400;
  box-shadow: 0 2px 8px rgba(245, 180, 0, 0.3);
}

.photo-preview {
  width: 100%;
  height: 100%;
}

.photo-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-select-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #4a90e2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 封面指示器样式 */
.cover-indicator {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.cover-indicator:hover {
  background-color: rgba(255, 255, 255, 0.9);
  color: #f5b400;
}

.cover-indicator.active {
  background-color: #f5b400;
  color: white;
}

/* 封面照片提示样式 */
.cover-photo-tip {
  margin: 16px 0;
  padding: 12px;
  background-color: rgba(74, 144, 226, 0.1);
  border-radius: 4px;
  border-left: 4px solid #4a90e2;
}

.cover-photo-tip p {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4a90e2;
}

/* 已选封面预览样式 */
.selected-cover {
  margin-top: 24px;
  padding: 16px;
  border: 1px solid #f5b400;
  border-radius: 8px;
  background-color: rgba(245, 180, 0, 0.05);
}

.selected-cover h4 {
  margin: 0 0 12px;
  color: #f5b400;
  display: flex;
  align-items: center;
  gap: 8px;
}

.selected-cover h4:before {
  content: '\f005';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.selected-cover .cover-preview {
  max-height: 200px;
  border-radius: 4px;
  overflow: hidden;
}

.selected-cover .cover-preview img {
  width: 100%;
  height: auto;
  max-height: 200px;
  object-fit: contain;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #666;
}

.loading-container i {
  font-size: 32px;
  color: #4a90e2;
  margin-bottom: 16px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  font-size: 16px;
}

/* 相册选择器样式 */
.album-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.album-select:focus {
  border-color: #4a90e2;
  outline: none;
}

/* 表单样式 */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #4a90e2;
  outline: none;
}

.required {
  color: #e53935;
}

.error-message {
  color: #e53935;
  margin-bottom: 16px;
  padding: 10px;
  background-color: rgba(229, 57, 53, 0.1);
  border-radius: 4px;
}

.cover-preview {
  margin-top: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.cover-preview img {
  width: 100%;
  height: auto;
  max-height: 300px;
  object-fit: contain;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, opacity 0.2s;
  border: none;
}

.btn-primary {
  background-color: #4a90e2;
  color: white;
}

.btn-primary:hover {
  background-color: #3a7bc8;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
}

.btn-secondary:hover {
  background-color: #e5e5e5;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 深色主题适配 */
[data-theme="dark"] .modal-container {
  background-color: var(--bg-card, #222);
  color: var(--text-primary, #eee);
}

[data-theme="dark"] .modal-header {
  border-bottom-color: var(--border-primary, #333);
}

[data-theme="dark"] .modal-header h2 {
  color: var(--text-primary, #eee);
}

[data-theme="dark"] .close-button {
  color: var(--text-secondary, #777);
}

[data-theme="dark"] .close-button:hover {
  color: var(--text-primary, #eee);
}

[data-theme="dark"] .modal-footer {
  border-top-color: var(--border-primary, #333);
}

[data-theme="dark"] .form-group label {
  color: var(--text-secondary, #ddd);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea,
[data-theme="dark"] .album-select {
  background-color: var(--bg-tertiary, #333);
  border-color: var(--border-primary, #444);
  color: var(--text-primary, #eee);
}

[data-theme="dark"] .form-group input:focus,
[data-theme="dark"] .form-group textarea:focus,
[data-theme="dark"] .album-select:focus {
  border-color: var(--accent-primary, #4a90e2);
}

[data-theme="dark"] .btn-secondary {
  background-color: var(--bg-tertiary, #333);
  color: var(--text-primary, #eee);
}

[data-theme="dark"] .btn-secondary:hover {
  background-color: var(--border-primary, #444);
}

[data-theme="dark"] .step-indicator::before {
  background-color: var(--border-primary, #444);
}

[data-theme="dark"] .step-number {
  background-color: var(--bg-tertiary, #333);
  border-color: var(--border-primary, #555);
  color: var(--text-secondary, #ddd);
}

[data-theme="dark"] .step-label {
  color: var(--text-secondary, #bbb);
}

[data-theme="dark"] .step-subtitle {
  color: var(--text-secondary, #ddd);
}

[data-theme="dark"] .location-card,
[data-theme="dark"] .photo-card {
  background-color: var(--bg-tertiary, #333);
  border-color: var(--border-primary, #444);
}

[data-theme="dark"] .location-card:hover,
[data-theme="dark"] .photo-card:hover {
  border-color: var(--accent-primary, #4a90e2);
  background-color: var(--bg-tertiary, #383838);
}

[data-theme="dark"] .location-card.selected {
  background-color: rgba(88, 166, 255, 0.15);
}

[data-theme="dark"] .location-details p {
  color: var(--text-muted, #aaa);
}

[data-theme="dark"] .empty-state,
[data-theme="dark"] .loading-container {
  color: var(--text-muted, #aaa);
}

[data-theme="dark"] .photo-card.is-cover {
  border-color: var(--accent-warning, #f5b400);
  box-shadow: 0 2px 8px rgba(227, 179, 65, 0.2);
}

[data-theme="dark"] .cover-indicator {
  background-color: var(--bg-overlay, rgba(51, 51, 51, 0.8));
  color: var(--text-muted, #aaa);
}

[data-theme="dark"] .cover-indicator:hover {
  background-color: var(--bg-overlay, rgba(51, 51, 51, 0.9));
  color: var(--accent-warning, #f5b400);
}

[data-theme="dark"] .cover-photo-tip {
  background-color: rgba(88, 166, 255, 0.05);
  border-left-color: var(--accent-primary, #4a90e2);
}

[data-theme="dark"] .selected-cover {
  background-color: rgba(227, 179, 65, 0.05);
  border-color: var(--accent-warning, #f5b400);
}

[data-theme="dark"] .selected-cover h4 {
  color: var(--accent-warning, #f5b400);
}