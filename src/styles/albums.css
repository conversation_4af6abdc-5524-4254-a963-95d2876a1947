/* 相册页面样式 */

/* 头部操作区域样式 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-actions .btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
  border-radius: 30px;
  padding: 0.85rem 1.5rem;
  font-weight: 600;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  text-decoration: none;
}

.header-actions .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.header-actions .btn-secondary {
  background-color: rgba(255, 255, 255, 0.9);
  color: #666;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.header-actions .btn-secondary:hover {
  background-color: rgba(255, 255, 255, 1);
  color: #333;
}

.albums-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.album-card {
  border-radius: 10px;
  overflow: hidden;
  background-color: var(--bg-card);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.album-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.album-cover {
  position: relative;
  height: 0;
  padding-bottom: 66.67%; /* 3:2 宽高比 */
  overflow: hidden;
}

.album-cover img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.album-card:hover .album-cover img {
  transform: scale(1.05);
}

.album-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
}

.album-name {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.album-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  opacity: 0.9;
}

.album-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.album-card:hover .album-actions {
  opacity: 1;
}

.album-actions button {
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.album-actions button:hover {
  background-color: white;
}

/* 删除按钮特殊样式 */
.album-actions .delete-btn {
  background-color: rgba(255, 220, 220, 0.8);
  color: #e53935;
}

.album-actions .delete-btn:hover {
  background-color: rgba(255, 200, 200, 0.9);
}

.album-actions .delete-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 加载、错误和空状态 */
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.loading-container i,
.error-container i,
.empty-container i {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

.error-container {
  color: #e53935;
}

.error-container i {
  color: #e53935;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .albums-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 15px;
  }

  .album-name {
    font-size: 1rem;
  }

  .album-meta {
    font-size: 0.75rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }

  .header-actions .btn {
    width: 100%;
    justify-content: center;
  }
}

/* 深色主题适配 */
[data-theme="dark"] .album-card {
  box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.3));
}

[data-theme="dark"] .album-actions button {
  background-color: var(--bg-overlay, rgba(50, 50, 50, 0.8));
  color: var(--text-primary, #eee);
}

[data-theme="dark"] .album-actions button:hover {
  background-color: var(--bg-tertiary, rgba(70, 70, 70, 0.9));
}

/* 深色主题删除按钮 */
[data-theme="dark"] .album-actions .delete-btn {
  background-color: rgba(248, 81, 73, 0.2);
  color: var(--accent-danger, #ff6b6b);
}

[data-theme="dark"] .album-actions .delete-btn:hover {
  background-color: rgba(248, 81, 73, 0.3);
}

[data-theme="dark"] .loading-container,
[data-theme="dark"] .empty-container {
  color: var(--text-muted, #999);
}