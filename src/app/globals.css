/* Tailwind CSS 引入指令 - Next.js项目中的基础样式配置 */
/* 导入自定义样式文件 - 确保模态框样式优先加载 */
@import '../styles/modal.css';
@import '../styles/layout.css';
@import '../styles/albums.css';
@import '../styles/upload.css';
@import '../styles/personal-space.css';

/* 导入Tailwind的基础样式，如重置和规范化样式 */
@tailwind base;
/* 导入Tailwind的组件类，提供预定义组件样式 */
@tailwind components;
/* 导入Tailwind的功能类，用于直接在HTML元素中应用样式 */
@tailwind utilities;

/* 全局CSS变量定义，用于主题颜色和样式一致性 */
:root {
  --primary: #6a7bd9;
  --primary-light: #8692e0;
  --primary-dark: #4f5eb8;
  --secondary: #f6866a;
  --accent: #6a4c93;
  --dark: #2a3052;
  --light: #f5f7ff;
  --gray: #e1e5f2;
  --gray-dark: #a0a8c0;
  --success: #4caf50;
  --danger: #f44336;
  --warning: #ff9800;
  --info: #2196f3;
  --shadow: 0 4px 12px rgba(42, 48, 82, 0.1);
  --shadow-dark: 0 8px 24px rgba(42, 48, 82, 0.15);
  --border-radius: 12px;
  --transition: all 0.3s ease;
  --background: #f5f7ff;
  --foreground: #2a3052;
}

/* 内联主题设置 - 定义Next.js主题变量 */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* 深色模式适配 - 使用媒体查询自动切换深色模式颜色 */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a1c2e;
    --foreground: #f5f7ff;
    --dark: #f5f7ff;
    --light: #1a1c2e;
    --gray: #3a3f5c;
    --gray-dark: #7c849b;
    --primary-dark: #5e6dcf;
    --accent: #8a64b8;
  }
}

/* 全局基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 主体样式设置 - 使用CSS变量确保主题一致性 */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  display: flex;
  overflow-x: hidden;
}

/* 基础元素样式 */
a {
  text-decoration: none;
  color: inherit;
}

button, .btn {
  cursor: pointer;
  border: none;
  outline: none;
  background: none;
  font-family: inherit;
  font-size: 1rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

input, select, textarea {
  font-family: inherit;
  font-size: 1rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--border-radius);
  padding: 10px 15px;
  outline: none;
  transition: var(--transition);
  color: var(--text-primary);
}

input:focus, select:focus, textarea:focus {
  border-color: var(--border-focus);
  background-color: var(--bg-primary);
  box-shadow: 0 0 0 2px rgba(106, 123, 217, 0.2);
}

/* Tailwind 组件层 - 在这里定义自定义组件样式 */
/* @layer components 允许你定义可以被Tailwind的工具类覆盖的组件类 */
@layer components {
  /* 侧边栏布局样式 */
  .sidebar {
    width: 260px;
    height: 100vh;
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--border-primary);
    position: fixed;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    z-index: 10;
  }

  .sidebar.collapsed {
    width: 80px;
  }

  .sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-primary);
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary);
  }

  .sidebar.collapsed .logo span {
    display: none;
  }

  .sidebar-menu {
    flex: 1;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 5px;
    overflow-y: auto;
  }

  .menu-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 20px;
    color: var(--text-secondary);
    transition: var(--transition);
  }

  .menu-item:hover, .menu-item.active {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
  }

  .sidebar.collapsed .menu-item span {
    display: none;
  }

  .sidebar-footer {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid var(--border-primary);
  }

  .profile-preview {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .sidebar.collapsed .profile-preview .user-name {
    display: none;
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
  }

  .main-container {
    margin-left: 260px;
    width: calc(100% - 260px);
    padding: 30px;
    transition: var(--transition);
  }

  .sidebar.collapsed ~ .main-container {
    margin-left: 80px;
    width: calc(100% - 80px);
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25px;
  }

  .page-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .notification-bell {
    position: relative;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 5px;
  }

  .badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--secondary);
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .content {
    padding-bottom: 30px;
  }

  /* 语言切换器样式 */
  .language-switch {
    position: relative;
    background-color: transparent;
    border-radius: 20px;
    padding: 0 5px;
    display: flex;
    align-items: center;
    margin-right: 15px;
    border: 1px solid var(--border-primary);
  }

  .language-btn {
    padding: 3px 8px;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: var(--transition);
    background: transparent;
    color: var(--text-tertiary);
  }

  .language-btn.active {
    background-color: var(--primary);
    color: white;
  }
  
  .language-divider {
    color: var(--border-primary);
    font-size: 0.8rem;
  }

  /* 仪表盘样式 */
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }

  .stat-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
  }

  .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
  }

  .stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .stat-title {
    color: var(--text-tertiary);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
  }

  .stat-icon.purple {
    background-color: var(--primary);
  }

  .stat-icon.orange {
    background-color: var(--secondary);
  }

  .stat-icon.blue {
    background-color: var(--info);
  }

  .stat-icon.green {
    background-color: var(--success);
  }

  .stat-icon.red {
    background-color: #e74c3c; /* Elegant red, can be replaced with a CSS variable if defined */
  }

  .stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 5px;
  }

  .stat-change {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
  }

  .stat-change.positive {
    color: #27ae60; /* 更明亮的绿色 */
    font-weight: 600;
  }

  .stat-change.negative {
    color: var(--danger);
  }

  .charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-top: 20px;
  }

  .chart-container {
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-md);
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .chart-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
  }

  .period-selector {
    display: flex;
    gap: 5px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 20px;
    padding: 5px;
  }

  .period-option {
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    color: var(--text-secondary);
  }

  .period-option.active {
    background-color: var(--bg-card);
    color: var(--accent-primary);
    box-shadow: var(--shadow-sm);
  }

  .chart-body {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .chart-placeholder {
    width: 100%;
    height: 100%;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .chart-placeholder::after {
    content: var(--chart-text);
    color: var(--text-tertiary);
    font-size: 1.2rem;
  }

  /* 地图容器样式 */
  .map-section {
    margin-top: 30px;
  }

  .map-container {
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-md);
  }

  .map-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .map-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
  }

  .map-subtitle {
    color: var(--text-tertiary);
  }

  .map-body {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
  }

  /* 时间轴样式 */
  .timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
  }

  .search-filter-group {
    display: flex;
    gap: 15px;
  }

  .search-box {
    position: relative;
    width: 300px;
  }

  .search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
  }

  .search-box input {
    padding-left: 40px;
    width: 100%;
  }

  .filter-dropdown select {
    min-width: 120px;
  }

  .timeline-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .timeline-item {
    display: flex;
    gap: 20px;
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 0;
    box-shadow: var(--shadow-md);
    overflow: hidden;
  }

  .timeline-media {
    flex: 0 0 300px;
    position: relative;
    overflow: hidden;
  }

  .timeline-media img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .media-type {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .timeline-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
  }

  .timeline-header-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .timeline-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-primary);
  }

  .timeline-date {
    color: var(--text-tertiary);
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
  }

  .timeline-description {
    color: var(--text-secondary);
    line-height: 1.5;
    flex: 1;
  }

  .timeline-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-primary);
  }

  .timeline-user {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .timeline-actions {
    display: flex;
    gap: 10px;
  }

  .timeline-actions button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: var(--transition);
  }

  .timeline-actions button:hover {
    background-color: var(--primary);
    color: white;
  }

  /* 相册墙样式 */
  .albums-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  .album-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
  }

  .album-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
  }

  .album-cover {
    position: relative;
    padding-top: 75%;
    overflow: hidden;
  }

  .album-cover img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .album-card:hover .album-cover img {
    transform: scale(1.05);
  }

  .album-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
  }

  .album-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
  }

  .album-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    opacity: 0.9;
  }

  .album-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 10px;
    opacity: 0;
    transition: var(--transition);
  }

  .album-card:hover .album-actions {
    opacity: 1;
  }

  .album-actions button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--bg-overlay);
    border: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
  }

  .album-actions button:hover {
    background-color: var(--bg-card);
    color: var(--accent-primary);
  }

  /* 心愿画卷样式 */
  .wishlist-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  .wishlist-section {
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--shadow-md);
  }

  .wishlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .wishlist-title {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-primary);
  }

  .wishlist-title i {
    color: var(--primary);
    font-size: 1.3rem;
  }

  .wishlist-title h2 {
    font-size: 1.4rem;
    font-weight: 600;
  }

  .wishlist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }

  .wishlist-card {
    background-color: var(--bg-card);
    border-radius: var(--border-radius);
    overflow: hidden;
    position: relative;
    border: 1px solid var(--border-primary);
    transition: var(--transition);
  }

  .wishlist-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }

  .wishlist-card-status {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    box-shadow: var(--shadow-sm);
  }

  .wishlist-card-status.wishlist {
    color: var(--primary);
  }

  .wishlist-card-status.planned {
    color: var(--warning);
  }

  .wishlist-card-status.completed {
    color: var(--success);
  }

  .wishlist-card-image {
    height: 160px;
    overflow: hidden;
  }

  .wishlist-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .wishlist-card:hover .wishlist-card-image img {
    transform: scale(1.05);
  }

  .wishlist-card-content {
    padding: 15px;
  }

  .wishlist-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
  }

  .wishlist-card-desc {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 12px;
    height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .wishlist-card-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.85rem;
  }

  .wishlist-date, .wishlist-priority {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
  }

  .wishlist-date.planned {
    color: var(--warning);
    background-color: rgba(255, 152, 0, 0.1);
  }

  .wishlist-date.completed {
    color: var(--success);
    background-color: rgba(76, 175, 80, 0.1);
  }

  .wishlist-priority.high {
    color: var(--danger);
    background-color: rgba(244, 67, 54, 0.1);
  }

  .wishlist-priority.medium {
    color: var(--warning);
    background-color: rgba(255, 152, 0, 0.1);
  }

  .wishlist-priority.low {
    color: var(--info);
    background-color: rgba(33, 150, 243, 0.1);
  }

  .wishlist-card-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transition: var(--transition);
  }

  .wishlist-card:hover .wishlist-card-actions {
    opacity: 1;
  }

  .wishlist-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    box-shadow: var(--shadow-sm);
  }

  .wishlist-action-btn:hover {
    background-color: var(--accent-primary);
    color: var(--text-inverse);
  }

  /* 账户页面样式 */
  .account-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 25px;
  }

  .card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--shadow-md);
  }

  .card-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-primary);
  }

  .couple-info {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
  }

  .couple-avatar-group {
    display: flex;
  }

  .couple-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
    margin-right: -10px;
    border: 3px solid white;
  }

  .couple-avatar:last-child {
    margin-right: 0;
    background-color: var(--secondary);
  }

  .couple-details {
    flex: 1;
  }

  .couple-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 5px;
  }

  .couple-date {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--gray-dark);
    font-size: 0.9rem;
  }

  .couple-date i {
    color: var(--secondary);
  }

  .form-group {
    margin-bottom: 18px;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 18px;
  }

  label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--dark);
  }

  .storage-info {
    margin-bottom: 20px;
  }

  .storage-bar {
    height: 10px;
    background-color: var(--gray);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
  }

  .storage-progress {
    height: 100%;
    width: 35%;
    background-color: var(--primary);
    border-radius: 10px;
  }

  .storage-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: var(--gray-dark);
    margin-bottom: 20px;
  }

  .storage-breakdown {
    margin-top: 15px;
  }

  .storage-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--gray);
  }

  .storage-item:last-child {
    border-bottom: none;
  }

  .storage-item-label {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .storage-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .storage-color.purple {
    background-color: var(--primary);
  }

  .storage-color.orange {
    background-color: var(--secondary);
  }

  .storage-color.blue {
    background-color: var(--info);
  }

  /* 模态框 */
  .wishlist-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
  }

  .wishlist-modal.show {
    opacity: 1;
    visibility: visible;
  }

  .wishlist-modal-container {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 25px;
    width: 500px;
    max-width: 90%;
    position: relative;
    box-shadow: var(--shadow-dark);
  }

  .wishlist-modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--gray);
    color: var(--dark);
    transition: var(--transition);
  }

  .wishlist-modal-close:hover {
    background-color: var(--danger);
    color: white;
  }

  .wishlist-modal-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark);
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--gray);
  }

  /* 按钮样式 */
  .btn {
    padding: 10px 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
  }

  /* 修改主要按钮样式，提高优先级 */
  .btn.btn-primary {
    background-color: var(--accent) !important; /* 使用!important确保优先级 */
    color: white !important;
    box-shadow: 0 3px 10px rgba(106, 76, 147, 0.4);
    font-weight: 700;
    font-size: 1.05rem;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.02em;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 12px 24px;
  }

  .btn.btn-primary:hover {
    background-color: #5a3f7c !important; /* 悬停时更深的紫色 */
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 76, 147, 0.5);
  }

  .btn-secondary {
    background-color: var(--secondary);
    color: white;
  }

  .btn-outline {
    border: 1px solid var(--gray-dark);
    color: var(--dark);
  }

  .btn-outline:hover {
    background-color: var(--gray);
  }

  /* 个人空间标签页样式 */
  .personal-content-tabs {
    display: flex;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--gray);
  }

  .personal-content-tabs .tab {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    color: var(--gray-dark);
    cursor: pointer;
    transition: var(--transition);
  }

  .personal-content-tabs .tab:hover {
    color: var(--dark);
  }

  .personal-content-tabs .tab.active {
    color: var(--primary);
    border-bottom: 2px solid var(--primary);
  }
}

/* 媒体瀑布流布局 */
.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  grid-gap: 20px;
  padding: 16px;
}

.media-item {
  break-inside: avoid;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: white;
  position: relative;
}

.media-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.image-container, .video-container {
  position: relative;
  width: 100%;
}

.media-image {
  width: 100%;
  height: auto;
  display: block;
  aspect-ratio: 1 / 1;
  object-fit: cover;
}

.video-thumbnail {
  position: relative;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
  height: 50px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.media-actions {
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 12px;
  display: flex;
  justify-content: flex-end;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.5));
  width: 100%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.media-item:hover .media-actions {
  opacity: 1;
}

.media-action-btn {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  margin-left: 12px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.media-action-btn:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    grid-gap: 12px;
    padding: 12px;
  }
}

/* Floating footprints button */
.floating-footprints-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: var(--bg-card, white);
  color: var(--accent-primary, #3b82f6);
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: var(--shadow-md, 0 2px 10px rgba(0, 0, 0, 0.1));
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  z-index: 40;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  border: 1px solid var(--border-primary, transparent);
}

.floating-footprints-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg, 0 4px 12px rgba(0, 0, 0, 0.15));
}

.count-badge {
  background-color: var(--accent-primary, #3b82f6);
  color: var(--text-inverse, white);
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Animation for the modal */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.animate-slideDown {
  animation: slideDown 0.3s ease-out;
}
